from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional
from datetime import date


# --- Nested User Schema ---
class UserCreate(BaseModel):
    email: EmailStr
    name: str
    mobile_number: str
    years_of_experience:int
    current_location:str
    skills:List[str]
    approved_by : str

# --- Employment History Schema ---
class EmploymentHistoryCreate(BaseModel):
    employer: str
    position: str
    period_start_date: date
    period_end_date: Optional[date] = None
    is_current_employment: bool
    description: str

# --- Main Candidate Creation Schema ---
class CreateCandidateSchema(BaseModel):
    
    user: UserCreate
    employment_history: List[EmploymentHistoryCreate]

class CandidateResponse(BaseModel):
    candidate_id: int
    message: str
