from sqlalchemy import Column, String, DateTime, Boolean, Foreign<PERSON>ey,Integer,Text
from sqlalchemy.dialects.mysql import CHAR
from datetime import datetime
import uuid

from config.db import Base

class VirtualWorkbench(Base):
    __tablename__ = "virtual_workbench"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    role_id = Column(Integer, ForeignKey("roles.role_id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.company_id"), nullable=False)

    job_title = Column(String(255), nullable=False)
    job_description = Column(Text, nullable=False)
    is_finalized = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.now())
