# import os
# from fastapi import APIRouter, Depends, HTTPException
# from sqlalchemy.orm import Session
# from config.db import SessionLocal
# from models.virtual_workbench import VirtualWorkbench
# from models.candidate import Candidate
# from services.resume_parser import ResumeRanker

# router = APIRouter()

# RESUME_FOLDER = r"C:/Evolvision Technologies/JD_Generation API/Resumes"

# def get_db():
#     db = SessionLocal()
#     try:
#         yield db
#     finally:
#         db.close()

# @router.get("/rank-resumes/by-vb/{vb_id}")
# def rank_candidates_by_vb(vb_id: int, db: Session = Depends(get_db)):
#     if not os.path.exists(RESUME_FOLDER):
#         raise HTTPException(status_code=500, detail="Resume folder path not found on server")

#     print("[DEBUG] Resume folder path:", RESUME_FOLDER)
#     print("[DEBUG] Files in folder:", os.listdir(RESUME_FOLDER))

#     # Load resume ranker
#     ranker = ResumeRanker(max_workers=6)

#     # Fetch virtual bench
#     vb = db.query(VirtualWorkbench).filter(VirtualWorkbench.id == vb_id).first()
#     if not vb:
#         raise HTTPException(status_code=404, detail=f"Virtual Bench ID {vb_id} not found")

#     jd_text = vb.job_description
#     clean_jd = ranker.remove_company_intro(jd_text)
#     required_experience = ranker.extract_experience_from_jd(clean_jd)

#     # Filter eligible candidates by experience
#     candidates = db.query(Candidate).filter(
#         Candidate.years_of_experience >= required_experience
#     ).all()
#     if not candidates:
#         raise HTTPException(status_code=404, detail="No eligible candidates found")

#     candidate_files = {
#         os.path.basename(c.resume_file_path.replace("\\", "/")).strip().lower()
#         for c in candidates
#     }
#     print("candidate_files:", candidate_files)

#     all_files = [f.strip().lower() for f in os.listdir(RESUME_FOLDER)]
#     print("all_files:", all_files)

#     filtered_files = [f for f in all_files if f in candidate_files]

#     if not filtered_files:
#         raise HTTPException(status_code=404, detail="No matching resumes found in folder")

#     # Run ranking
#     ranker_output = ranker.rank_resumes(RESUME_FOLDER, jd_text, top_n=2)

#     # Map file → candidate
#     resume_to_candidate = {
#         os.path.basename(c.resume_file_path.replace("\\", "/")).strip().lower(): c
#         for c in candidates
#     }
#     print("ranker_output filenames:", [os.path.basename(r['filename']).strip().lower() for r in ranker_output])
#     print("resume_to_candidate keys:", resume_to_candidate.keys())

#     # Join results
#     ranked_with_details = []
#     for r in ranker_output:
#         file_name = os.path.basename(r["filename"]).strip().lower()
#         if file_name not in candidate_files:
#             print(f"File {file_name} not in candidate_files, skipping.")
#         if file_name in candidate_files:
#             candidate = resume_to_candidate.get(file_name)
#             if candidate:
#                 ranked_with_details.append({
#                     "candidate_id": candidate.id,
#                     "name": candidate.name,
#                     "email": candidate.email,
#                     "score": r["sbert_score"],
#                     "filename": r['filename'],
#                     "llm_score": r.get("llm_score", 0.0),
#                     "llm_matches": r.get("llm_matches", "")
#                 })

#     return {
#         "virtual_bench_id": vb_id,
#         "required_experience_years": required_experience,
#         "ranked_resumes": ranked_with_details
#     }

import os
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from config.db import SessionLocal
from models.virtual_workbench import VirtualWorkbench
from models.candidate import Candidate
from models.candidate_employment import CandidateEmployment
from schemas.virtualbench_candidate import VirtualBenchCandidateCreate
from services.virtualbench_candidate_service import create_or_update_virtualbench_candidate
from services.resume_parser import ResumeRanker

router = APIRouter()

RESUME_FOLDER = r"C:/Evolvision Technologies/JD_Generation API/Resumes"

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def normalize_filename(path: str) -> str:
    return (
        os.path.basename(path.replace("\\", "/"))
        .strip()
        .lower()
        .replace(" ", "")
        .replace("_", "")
    )

@router.get("/rank-resumes/by-vb/{vb_id}")
def rank_candidates_by_vb(vb_id: int, db: Session = Depends(get_db)):
    if not os.path.exists(RESUME_FOLDER):
        raise HTTPException(status_code=500, detail="Resume folder path not found on server")

    print("[DEBUG] Resume folder path:", RESUME_FOLDER)

    ranker = ResumeRanker(max_workers=6)

    # --- Fetch Virtual Workbench ---
    vb = db.query(VirtualWorkbench).filter(VirtualWorkbench.id == vb_id).first()
    if not vb:
        raise HTTPException(status_code=404, detail=f"Virtual Bench ID {vb_id} not found")

    # clean_jd = ranker.remove_company_intro(vb.job_description)
    # required_experience = ranker.extract_experience_from_jd(clean_jd)
    job_title = vb.job_title.strip().lower()

    # --- Filter Candidates ---
    all_candidates = db.query(Candidate).filter(Candidate.approved_by.is_not(None)).all()

    if not all_candidates:
        raise HTTPException(status_code=404, detail="No eligible candidates found")

    # --- Match Candidates by Job Title in Employment History ---
    matching_candidates = []
    for candidate in all_candidates:
        employments = db.query(CandidateEmployment).filter_by(candidate_id=candidate.id).all()
        for emp in employments:
            if emp.position and job_title in emp.position.strip().lower():
                matching_candidates.append(candidate)

    # --- Fallback if No Match Found ---
    if not matching_candidates:
        print("[WARN] No job title matches found")
        return {
        "virtual_bench_id": vb_id,
        "message": "No candidates found whose employment position matches the job title in the virtual bench.",
        "ranked_resumes": []
    }

    # --- Match Resume Files in Folder ---
    candidate_files = {
        normalize_filename(c.resume_file_path): c for c in matching_candidates if c.resume_file_path
    }

    all_files = [f for f in os.listdir(RESUME_FOLDER) if f.lower().endswith(('.pdf', '.docx', '.txt'))]
    all_file_map = {normalize_filename(f): f for f in all_files}
    matched_files = [f for key, f in all_file_map.items() if key in candidate_files]

    if not matched_files:
        raise HTTPException(status_code=404, detail="No matching resumes found in folder")

    print(f"[INFO] {len(matched_files)} resumes matched with candidates.")

    matched_resume_files = [
    os.path.join(RESUME_FOLDER, f)
    for f in os.listdir(RESUME_FOLDER)
    if normalize_filename(os.path.join(RESUME_FOLDER, f)) in {
        normalize_filename(c.resume_file_path) for c in matching_candidates if c.resume_file_path
    }
]

    # --- Run GPT LLM Resume Ranking ---
    ranked_resumes = ranker.rank_resumes(
        db=db,
        vb_id=vb_id,
        resume_paths=matched_resume_files,
        top_n=5
    )

    # --- Save Results & Return Response ---
    ranked_with_details = []
    for r in ranked_resumes:
        create_or_update_virtualbench_candidate(
            db=db,
            data=VirtualBenchCandidateCreate(
                virtual_bench_id=vb_id,
                candidate_id=r["candidate_id"],
                llm_score=r.get("llm_category", None),
                llm_analysis=r.get("llm_analysis", ""),
                prompt_id=r.get("prompt_id")
            )
        )

        ranked_with_details.append({
            "candidate_id": r["candidate_id"],
            "name": r["name"],
            "email": r["email"],
            "llm_score": r.get("llm_category", None),
            "llm_feedback": r.get("llm_analysis", ""),
            "filename": r["filename"]
        })

    return {
        "virtual_bench_id": vb_id,
        "ranked_resumes": ranked_with_details
    }
