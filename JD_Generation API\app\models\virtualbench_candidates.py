from sqlalchemy import <PERSON>umn, Integer, ForeignKey, Float, Text, DateTime, UniqueConstraint,String
from sqlalchemy.sql import func
from config.db import Base

class VirtualBenchCandidate(Base):
    __tablename__ = "virtualbench_candidates"

    id = Column(Integer, primary_key=True, index=True)

    virtual_bench_id = Column(Integer, ForeignKey("virtual_workbench.id"), nullable=False)
    candidate_id = Column(Integer, ForeignKey("candidates.id"), nullable=False)
    prompt_id = Column(Integer, ForeignKey("prompts.id"), nullable=True)

    llm_score = Column(String, nullable=True)
    llm_analysis = Column(Text, nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    __table_args__ = (
        UniqueConstraint("virtual_bench_id", "candidate_id", name="uq_virtualbench_candidate"),
    )
