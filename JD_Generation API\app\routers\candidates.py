from fastapi import APIRouter, Depends, UploadFile, File, Form, HTTPException
from sqlalchemy.orm import Session
from config.db import get_db
from models.candidate import Candidate
from models.candidate_employment import CandidateEmployment
import os, uuid, shutil, json
from typing import Optional
from services.text_extractor import extract_resume_text

router = APIRouter()

UPLOAD_DIR = "C:/Evolvision Technologies/JD_Generation API/Resumes"


@router.post("/candidates/")
async def create_candidate_with_employment(
    name: str = Form(...),
    email: str = Form(...),
    mobile_number: str = Form(...),
    years_of_experience: float = Form(...),
    current_location: str = Form(...),
    resume: UploadFile = File(...),
    approved_by : Optional[str] = Form(None),
    skills: Optional[str] = Form(None),
    employment_history: Optional[str] = Form("[]"), 
    db: Session = Depends(get_db)
):
    try:
        # Save resume file
        filename = f"{resume.filename}"
        file_path = os.path.join(UPLOAD_DIR, filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(resume.file, buffer)

        # Parse resume text
        parsed_resume_text = extract_resume_text(file_path)
        if not parsed_resume_text or not parsed_resume_text.strip():
         raise HTTPException(status_code=400, detail="Resume could not be parsed. Please upload a valid resume file.")

        # Store candidate
        candidate = Candidate(
            name=name,
            email=email,
            mobile_number=mobile_number,
            years_of_experience=years_of_experience,
            current_location=current_location,
            # linkedin_url=linkedin_url,
            skills=skills,
            approved_by=approved_by,
            resume_file_path=file_path,
            parsed_resume_text=parsed_resume_text
        )
        db.add(candidate)
        db.commit()
        db.refresh(candidate)

        # Parse and insert employment history
        try:
            employment_records = json.loads(employment_history)
            for emp in employment_records:
                employment = CandidateEmployment(
                    candidate_id=candidate.id,
                    employer=emp["employer"],
                    position=emp["position"],
                    period_start_date=emp["period_start_date"],
                    period_end_date=emp.get("period_end_date"),
                    is_current_employment=emp["is_current_employment"],
                    description=emp["description"]
                )
                db.add(employment)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid employment history format: {e}")

        db.commit()

        return {
            "candidate_id": candidate.id,
            "message": "Candidate and employment history created successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
