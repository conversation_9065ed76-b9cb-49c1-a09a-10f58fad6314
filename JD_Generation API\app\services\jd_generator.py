import time
import re
from dotenv import load_dotenv
from sqlalchemy.orm import Session
from models.prompts import Prompt, PromptTypeEnum

load_dotenv()

class JDGeneratorService:
    def __init__(self, openai_client, company_name: str, company_desc: str):
        self.openai_client = openai_client
        self.company_name = company_name
        self.company_desc = company_desc

    def remove_links(self,text: str) -> str:
     text = re.sub(r'\(\[[^\]]+\]\([^)]+\)\)', '', text)

     text = re.sub(r'\[[^\]]+\]\([^)]+\)', '', text)

     text = re.sub(r'https?://\S+', '', text)

     text = re.sub(r'\(\s*\)', '', text)
     text = re.sub(r'\[\s*\]', '', text)

     return text.strip()
    def clean_html_text(self,raw_text: str) -> str:
     text = raw_text.replace("\\n", "")
     text = text.replace("\n", "")
     text = re.sub(r'\s{2,}', ' ', text)
     text = re.sub(r'^(\\n|\\\\n|\n|\r|\s)+', '', text)
     return text.strip()

    async def generate_jd(
        self,
        db: Session,
        role_name: str,
        level: str,
        location: str,
        mode: str,
    ) -> str:
        try:
            print(f"\nGenerating JD for {role_name} - {level}...")

            prompt_obj = db.query(Prompt).filter_by(type=PromptTypeEnum.JOB_DESC).order_by(Prompt.created_at.desc()).first()

            if not prompt_obj or not prompt_obj.description:
             raise ValueError("No Company description prompt found in the database (type=JOB_DESC)")
        
            prompt = prompt_obj.description.strip()
            user_prompt = f"Role Name: {role_name} Level: {level} Location: {location} Mode: {mode}"

            start_time = time.time()

            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {
                        "role": "system",
                        "content": prompt
                    },
                    {"role": "user", "content":user_prompt}
                ]
            )

            jd = response.choices[0].message.content
            jd_cleaned = self.remove_links(jd)
            formated_respone = self.clean_html_text(jd_cleaned)

            jd_final = f"""{self.company_name} - {self.company_desc}\n\n{formated_respone}"""

            end_time = time.time()
            print(f"JD Generated in {end_time - start_time:.2f} seconds")

            return jd_final

        except Exception as e:
            print(f"Error generating JD: {str(e)}")
            return "JD generation failed due to an internal error."
        
    # async def enhance_existing_jd(
    #     self,
    #     role_name: str,
    #     level: str,
    #     location: str,
    #     mode: str,
    #     raw_jd: str,
        
    # ) -> str:
    #  prompt = f"""
    #              You are a professional HR specialist and job description writer.

    #              Take the following raw job description for the role of **{role_name}** and enhance it for clarity, structure, and professional tone.

    #              ### Original JD:
    #              {raw_jd}

    #              ### Company Details:
    #              - Company Name: {self.company_name}
    #              - Experience Level: {level}
    #              - Location: {location}
    #              - Employment Type: {mode}

    #              ### Output Format:
    #              1. **Role Overview** – Brief summary of the role and its impact  
    #              2. **Skills Required** – List the key hard and soft skills  
    #              3. **Qualifications** – Minimum education and Write a years of experience expected in Numbers
    #              4. **Key Responsibilities** – Bullet points of what the candidate will do

    #              Make it clean, accurate, and attractive to top candidates.  
    #              Do **NOT** use markdown, emojis, citations, or unnecessary fluff."""

    
    #  response = await self.openai_client.chat.completions.create(
    #     model="gpt-4o-mini",
    #     messages=[
    #         {"role": "system", "content": "You are a professional recruiter and copywriter."},
    #         {"role": "user", "content": prompt}
    #     ]
    # )
    #  jd = response.choices[0].message.content.strip()
    #  jd_cleaned = self.remove_links(jd)

    #  jd_final = f"""{self.company_name} - {self.company_desc}\n\n{jd_cleaned}"""
    #  return jd_final
    
    