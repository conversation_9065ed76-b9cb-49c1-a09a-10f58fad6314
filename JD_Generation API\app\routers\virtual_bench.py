from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime
from typing import List
from config.db import SessionLocal
from models.virtual_workbench import VirtualWorkbench
from models.role import Role
from models.company import Company
from schemas.virtual_bench import VirtualBenchResponse,JDEnhancementInput,ManualJDInput
from services.jd_generator import JDGeneratorService
from openai import AsyncOpenAI
import os
from dotenv import load_dotenv

load_dotenv()

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.get("/", response_model=List[VirtualBenchResponse])
def get_all_workbench(db: Session = Depends(get_db)):
    return db.query(VirtualWorkbench).all()

@router.post("/augment/{role_id}", response_model=VirtualBenchResponse)

async def augment_jd(role_id: int, db: Session = Depends(get_db)):
    role = db.query(Role).filter(Role.role_id == role_id).first()
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    company = db.query(Company).filter(Company.company_id == role.company_id).first()
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")
    
    existing_vb = db.query(VirtualWorkbench).filter(
        VirtualWorkbench.company_id == company.company_id,
        VirtualWorkbench.role_id == role.role_id
    ).first()

    if existing_vb:
        return existing_vb
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise HTTPException(status_code=500, detail="OPENAI_API_KEY environment variable is not set")
    
    openai_client = AsyncOpenAI(api_key=api_key)

    # Instantiate AI JD Generator with company data
    jd_generator = JDGeneratorService(
        openai_client=openai_client,
        company_name=company.name,
        company_desc=company.description or "",
        
    )

    try:
        # Generate the JD using role + company info
        jd_text = await jd_generator.generate_jd(
            db=db,
            role_name=role.role_name,
            level=role.experience_level,
            location=role.location,
            mode=role.developer_mode,
            
        )

        # If response is empty or clearly failed
        if not jd_text or "JD generation failed" in jd_text:
            raise HTTPException(status_code=500, detail="AI failed to generate a valid Job Description")

        # Save to DB only if JD is successfully generated
        new_vb = VirtualWorkbench(
            role_id=role.role_id,
            company_id=role.company_id,
            job_title=role.role_name,
            job_description=jd_text,
            is_finalized=False
        )

        db.add(new_vb)
        db.commit()
        db.refresh(new_vb)

        return new_vb

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating JD: {str(e)}")

@router.post("/virtual-bench/{vb_id}/enhance-jd")
async def enhance_job_description(
    vb_id: int,
    input: JDEnhancementInput,
    db: Session = Depends(get_db)
):
    vb = db.query(VirtualWorkbench).filter(VirtualWorkbench.id == vb_id).first()
    if not vb:
        raise HTTPException(status_code=404, detail="Virtual Bench not found")

    if not input.updated_jd.strip():
        raise HTTPException(status_code=400, detail="Job Description is required")

    role = db.query(Role).filter(Role.role_id == vb.role_id).first()
    if not role:
        raise HTTPException(status_code=404, detail="Related Role not found")

    company = db.query(Company).filter(Company.company_id == vb.company_id).first()
    if not company:
        raise HTTPException(status_code=404, detail="Related Company not found")

    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise HTTPException(status_code=500, detail="Missing OPENAI_API_KEY")

    openai_client = AsyncOpenAI(api_key=api_key)
    jd_enhancer = JDGeneratorService(openai_client,company_name=company.name,
        company_desc=company.description or "",)

    try:
        enhanced = await jd_enhancer.enhance_existing_jd(
            role_name=vb.job_title,
            raw_jd=input.updated_jd,
            level=role.experience_level,
            location=role.location,
            mode=role.developer_mode,
            
        )

        vb.job_description = enhanced.strip()
        db.commit()
        db.refresh(vb)

        return {
            "virtual_bench_id": vb.id,
            "enhanced_jd": vb.job_description
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Enhancement failed: {str(e)}")

@router.post("/manual-jd/{role_id}", response_model=VirtualBenchResponse)
def create_manual_jd(
    role_id: int,
    input: ManualJDInput,
    db: Session = Depends(get_db)
):
    # Fetch role
    role = db.query(Role).filter(Role.role_id == role_id).first()
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    # Fetch company
    company = db.query(Company).filter(Company.company_id == role.company_id).first()
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Prevent duplicate JD for same company+role
    existing_vb = db.query(VirtualWorkbench).filter(
        VirtualWorkbench.company_id == company.company_id,
        VirtualWorkbench.role_id == role.role_id
    ).first()

    if existing_vb:
        raise HTTPException(status_code=400, detail="JD already exists for this role")

    # Add manually entered JD
    new_vb = VirtualWorkbench(
        role_id=role.role_id,
        company_id=company.company_id,
        job_title=role.role_name,
        job_description=input.job_description.strip(),
        is_finalized=True  # Mark as finalized if it's manually added
    )

    db.add(new_vb)
    db.commit()
    db.refresh(new_vb)

    return new_vb