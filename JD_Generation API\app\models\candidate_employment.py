from sqlalchemy import Column, Integer, String, Date, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Text
from sqlalchemy.orm import relationship
from config.db import Base 

class CandidateEmployment(Base):
    __tablename__ = "candidate_employments"

    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(Integer, ForeignKey("candidates.id"), nullable=False)
    employer = Column(String(255), nullable=False)
    position = Column(String(255), nullable=False)
    period_start_date = Column(Date, nullable=False)
    period_end_date = Column(Date, nullable=True)
    is_current_employment = Column(Boolean, default=False)
    description = Column(Text, nullable=True)

    # Optional relationship if you want to access employment from candidate
    candidate = relationship("Candidate", back_populates="employments")
