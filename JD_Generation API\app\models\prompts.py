from sqlalchemy import Column, Integer, String, Text, Enum,DateTime
from config.db import Base
import enum
from datetime import datetime


class PromptTypeEnum(str, enum.Enum):
    VB_MATCH = "VB_MATCH"
    COMPANY_DESC = "COMPANY_DESC"
    JOB_DESC = "JOB_DESC"
    VB_RECOMMENDATION = "VB_RECOMMENDATION"

class Prompt(Base):
    __tablename__ = "prompts"

    id = Column(Integer, primary_key=True, index=True)
    description = Column(Text, nullable=True)
    version = Column(String(10), nullable=False)
    type = Column(Enum(PromptTypeEnum), nullable=False)
    created_at = Column(DateTime, default=datetime.now())
