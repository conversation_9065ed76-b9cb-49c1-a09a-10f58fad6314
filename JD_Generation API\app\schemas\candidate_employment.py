from pydantic import BaseModel
from typing import Optional
from datetime import date

class CandidateEmploymentBase(BaseModel):
    candidate_id: int
    employer: str
    position: str
    period_start_date: date
    period_end_date: Optional[date] = None
    is_current_employment: bool = False
    description: Optional[str] = None

class CandidateEmploymentCreate(BaseModel):
    employer: str
    position: str
    period_start_date: date
    period_end_date: Optional[date]
    is_current_employment: Optional[bool] = False
    description: Optional[str]

class CandidateEmploymentUpdate(BaseModel):
    employer: Optional[str]
    position: Optional[str]
    period_start_date: Optional[date]
    period_end_date: Optional[date]
    is_current_employment: Optional[bool]
    description: Optional[str]

class CandidateEmploymentResponse(CandidateEmploymentBase):
    id: int

    class Config:
        orm_mode = True
