from sqlalchemy import Colum<PERSON>, <PERSON>, Inte<PERSON>, Enum, ForeignKey
from sqlalchemy.dialects.mysql import CHAR
import enum
import uuid

from config.db import Base

class ExperienceLevel(str, enum.Enum):
    junior = "Junior"
    mid = "Mid"
    senior = "Senior"
    executive = "Executive"

class DeveloperMode(str, enum.Enum):
    full_time = "Full-time"
    part_time = "Part-time"
    contract = "Contract-based"

class Role(Base):
    __tablename__ = "roles"

    role_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    company_id = Column(Integer, ForeignKey("companies.company_id"), nullable=False)
    role_name = Column(String(255), nullable=False)
    experience_level = Column(Enum(ExperienceLevel), nullable=False)
    number_of_developers = Column(Integer, nullable=False)
    developer_mode = Column(Enum(DeveloperMode), nullable=False)
    duration = Column(String(100)) 
    location = Column(String(255))
