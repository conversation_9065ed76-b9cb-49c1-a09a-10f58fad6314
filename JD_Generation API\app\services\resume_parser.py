import os
import re
import time
from typing import List, Dict
from sqlalchemy.orm import Session
from openai import OpenAI
from concurrent.futures import ThreadPoolExecutor, as_completed
from services.text_extractor import extract_resume_text
from models.virtual_workbench import VirtualWorkbench
from models.candidate import Candidate
from models.role import Role
from models.company import Company
from models.candidate_employment import CandidateEmployment
from dotenv import load_dotenv
from models.prompts import Prompt, PromptTypeEnum
from services.vb_candidate_match_sys_prompt import system_prompt

load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))


class ResumeRanker:
    def __init__(self, max_workers: int = 4):
        # Initialize ResumeRanker with a specified number of worker threads for parallel processing
        self.max_workers = max_workers

    def normalize_filename(self, path: str) -> str:
        """Standardize resume filenames for matching by removing spaces, underscores, and converting to lowercase."""
        return (
            os.path.basename(path.replace("\\", "/"))
            .strip()
            .lower()
            .replace(" ", "")
            .replace("_", "")
        )

    def extract_text(self, file_path: str) -> str:
        # Extracts text from a resume file using the text_extractor service
        return extract_resume_text(file_path)

    def build_llm_inputs(self, db: Session, vb_id: int, resume_paths: List[str]) -> List[Dict]:
        """
        Prepares the input messages for the LLM (OpenAI) for each resume:
        - Fetches VirtualWorkbench, Company, and Prompt info from DB
        - Matches resumes to candidates in DB
        - Gathers employment history and resume text
        - Constructs a list of message dicts for LLM analysis
        """
        vb = db.query(VirtualWorkbench).filter_by(id=vb_id).first()
        # role = db.query(Role).filter_by(role_id=vb.role_id).first()
        company = db.query(Company).filter_by(company_id=vb.company_id).first()
        jd_text = vb.job_description

        # Fetch the resume evaluation prompt from the DB
        prompt_obj = db.query(Prompt).filter_by(type=PromptTypeEnum.VB_MATCH).order_by(Prompt.created_at.desc()).first()
        if not prompt_obj or not prompt_obj.description:
            raise ValueError("No resume evaluation prompt found in the database (type=VB_MATCH)")
        # prompt = prompt_obj.description.strip()
        prompt_id = prompt_obj.id

        candidates = db.query(Candidate).all()
        candidate_map = {
            self.normalize_filename(c.resume_file_path): c
            for c in candidates if c.resume_file_path
        }

        prepared = []
        for path in resume_paths:
            normalized = self.normalize_filename(path)
            print(f"[DEBUG] Trying to match resume: {normalized}")
            candidate = candidate_map.get(normalized)

            if not candidate:
                print(f"[WARN] No candidate matched for file: {os.path.basename(path)}")
                continue

            # Gather employment history for the candidate
            employments = db.query(CandidateEmployment).filter_by(candidate_id=candidate.id).all()
            employment_text = "\n".join([
                f"- {e.position} at {e.employer} ({e.period_start_date} to {e.period_end_date or 'Present'}): {e.description}"
                for e in employments
            ]) or "No employment history found."

            # Use parsed_resume_text from DB if available, else fallback to parsing
            resume_text = candidate.parsed_resume_text
            if not resume_text or not resume_text.strip():
                resume_text = extract_resume_text(path)
                candidate.parsed_resume_text = resume_text
                db.commit()
            if not resume_text.strip():
                print(f"[WARN] Empty resume text for {path}")
                continue

            #Print resume text for debugging
            print("Resume Text : ",resume_text)

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Company Description:\n{company.description or 'Not Available'}"},
                {"role": "user", "content": f"Virtual Bench Info:\nTitle: {vb.job_title}"},
                {"role": "user", "content": f"Job Description:\n{jd_text}"},
                {"role": "user", "content": f"Candidate Info:\nName: {candidate.name}\nEmail: {candidate.email}"},
                {"role": "user", "content": f"Employment History:\n{employment_text}"},
                {"role": "user", "content": f"Resume Text:\n{resume_text}"}
            ]

            prepared.append({
                "filename": os.path.basename(path),
                "messages": messages,
                "path": path,
                "candidate_id": candidate.id,
                "name": candidate.name,
                "email": candidate.email,
                "prompt_id":prompt_id
            })

        return prepared
    
    def clean_llm_response(self,raw_text: str) -> str:
        return re.sub(r'\s{2,}', ' ', re.sub(r'\n', '', re.sub(r'\\n', '', raw_text))).strip()

    def analyze_with_messages(self, messages: List[Dict]) -> tuple:
        """
        Sends the prepared messages to the OpenAI LLM and parses the response:
        - Extracts the score from the response using regex
        - Returns the score and the cleaned response text
        """
        try:
            response = client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=messages,
                temperature=0.0,
                max_tokens=1000,
                top_p = 1.0,
                seed = 42,
            )
            response_text = response.choices[0].message.content.strip()
            response_text = self.clean_llm_response(response_text)
            score_match = re.search(r"<b>CATEGORY</b><br>\s*(Excellent|Very Good|Good|Average|Below Average|Bad candidate)", response_text, re.IGNORECASE)
            category = score_match.group(1) if score_match else None
            cleaned_response = re.sub(
    r"<b>CATEGORY</b><br>\s*(Excellent|Very Good|Good|Average|Below Average|Bad candidate)<br>?",
    "",
    response_text,
    flags=re.IGNORECASE
).strip()
            without_score_section = re.sub(
                r"^(<br>\s*){1,2}", "", cleaned_response, flags=re.IGNORECASE
            )
            formated_respone = re.sub(
                r"\s{2,}",
                " ",
                re.sub(r"\n", "", re.sub(r"\\n", "", without_score_section)),
            ).strip()
            print(messages)

            return category, formated_respone
        except Exception as e:
            return None, f"Error in GPT analysis: {e}"

    def rank_resumes(self, db: Session, vb_id: int, resume_paths: List[str], top_n: int = 10) -> List[Dict]:
        """
        Ranks resumes by sending them to the LLM for analysis in parallel:
        - Prepares LLM inputs for each resume
        - Uses ThreadPoolExecutor for concurrent LLM calls
        - Collects and sorts results by score, returning the top N
        """
        start = time.time()
        print(f"\n[START] GPT-only Resume ranking for VB ID: {vb_id}\n")


        inputs = self.build_llm_inputs(db, vb_id, resume_paths)
        ranked_results = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {
                executor.submit(self.analyze_with_messages, item["messages"]): item for item in inputs
            }
            for future in as_completed(futures):
                meta = futures[future]
                try:
                    score, full_analysis = future.result()
                    ranked_results.append({
                        "candidate_id": meta["candidate_id"],
                        "name": meta["name"],
                        "email": meta["email"],
                        "filename": meta["filename"],
                        "llm_category": score, 
                        "llm_analysis": full_analysis,
                        "prompt_id": meta["prompt_id"]
                    })
                except Exception as e:
                    ranked_results.append({
                        "candidate_id": meta["candidate_id"],
                        "name": meta["name"],
                        "email": meta["email"],
                        "filename": meta["filename"],
                        "llm_category": None,
                        "llm_analysis": f"LLM analysis failed: {e}",
                        "prompt_id": meta["prompt_id"]
                    })
        category_rank = {
    "Excellent": 6,
    "Very Good": 5,
    "Good": 4,
    "Average": 3,
    "Below Average": 2,
    "Bad candidate": 1
}
        def get_rank(entry):
          category = entry.get("llm_category", "")
          return category_rank.get(category, 0)
        print(f"\n✅ Completed GPT-only resume ranking in {time.time() - start:.2f}s")
        return sorted(ranked_results, key=get_rank, reverse=True)[:top_n]

    def rank_resumes_by_virtualbench(self, vb_id: int, db: Session, resume_folder: str, top_n: int = 10) -> List[Dict]:
        """
        Filters and ranks resumes for a given VirtualWorkbench:
        - Filters resumes in the folder to those matching candidates in the DB
        - Calls rank_resumes to perform LLM-based ranking
        """
        vb = db.query(VirtualWorkbench).filter(VirtualWorkbench.id == vb_id).first()
        if not vb:
            raise ValueError(f"VirtualWorkbench with id {vb_id} not found.")

        candidates = db.query(Candidate).all()
        candidate_files = {
            self.normalize_filename(c.resume_file_path) for c in candidates if c.resume_file_path
        }

        all_files = [f for f in os.listdir(resume_folder) if f.lower().endswith(('.pdf', '.docx', '.txt'))]
        filtered_files = [f for f in all_files if self.normalize_filename(f) in candidate_files]

        print(f"\nFiltered {len(filtered_files)} resumes from {len(all_files)} total files based on DB matches.")
        resume_paths = [os.path.join(resume_folder, f) for f in filtered_files]

        return self.rank_resumes(db=db, vb_id=vb_id, resume_folder=resume_folder, top_n=top_n)