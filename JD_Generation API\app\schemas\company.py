from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class CompanyBase(BaseModel):
    name: str
    description: Optional[str] = None
    headquarter: str
    url:str


class CompanyCreate(CompanyBase):
    pass

class CompanyResponse(CompanyBase):
    company_id: int
    created_at: datetime

    class Config:
        orm_mode = True

class EnhancementInput(BaseModel):
    updated_description: str
