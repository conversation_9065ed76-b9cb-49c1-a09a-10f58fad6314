from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from config.db import SessionLocal
from models.role import Role
from models.company import Company
from schemas.role import RoleCreate, RoleResponse

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/{company_id}/roles", response_model=RoleResponse)
def create_role(company_id: int, role: RoleCreate, db: Session = Depends(get_db)):
    if not db.query(Company).filter(Company.company_id == company_id).first():
        raise HTTPException(status_code=404, detail="Company not found")

    new_role = Role(company_id=company_id, **role.dict())
    db.add(new_role)
    db.commit()
    db.refresh(new_role)
    return new_role
