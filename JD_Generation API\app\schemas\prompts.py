from pydantic import BaseModel
from typing import Optional
from enum import Enum


class PromptTypeEnum(str, Enum):
    VB_MATCH = "VB_MATCH"
    COMPANY_DESC = "COMPANY_DESC"
    JOB_DESC = "JOB_DESC"
    VB_RECOMMENDATION = "VB_RECOMMENDATION"


# For creating a new prompt
class PromptCreate(BaseModel):
    description: Optional[str] = None
    version: str = "v1"
    type: PromptTypeEnum


# Response schema
class PromptResponse(BaseModel):
    id: int
    description: Optional[str]
    version: str
    type: PromptTypeEnum

    class Config:
        orm_mode = True
