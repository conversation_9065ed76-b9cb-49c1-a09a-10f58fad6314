from pydantic import BaseModel
from uuid import UUID
from datetime import datetime
from typing import Optional

class VirtualBenchBase(BaseModel):
    job_title: str
    job_description: str
    is_finalized: Optional[bool] = False

class VirtualBenchCreate(VirtualBenchBase):
    pass

class VirtualBenchResponse(VirtualBenchBase):
    id: int
    role_id: int
    company_id: int
    created_at: datetime

    class Config:
        orm_mode = True

class JDEnhancementInput(BaseModel):
    updated_jd: str

class ManualJDInput(BaseModel):
    job_description: str
