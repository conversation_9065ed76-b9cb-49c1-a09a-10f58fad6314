from openai import AsyncOpenAI
import re
from dotenv import load_dotenv
from models.prompts import Prompt, PromptTypeEnum
from sqlalchemy.orm import Session

load_dotenv()

class AICompanyDescriber:
    def __init__(self, client: AsyncOpenAI):
        self.client = client
    
    def remove_links(self,text: str) -> str:
     text = re.sub(r'\(\[[^\]]+\]\([^)]+\)\)', '', text)

     text = re.sub(r'\[[^\]]+\]\([^)]+\)', '', text)

     text = re.sub(r'https?://\S+', '', text)

     text = re.sub(r'\(\s*\)', '', text)
     text = re.sub(r'\[\s*\]', '', text)

     return text.strip()

    async def generate_company_description(self,db: Session,company_name: str, company_url: str) -> str:

        prompt_obj = db.query(Prompt).filter_by(type=PromptTypeEnum.COMPANY_DESC).order_by(Prompt.created_at.desc()).first()

        if not prompt_obj or not prompt_obj.description:
            raise ValueError("No Company description prompt found in the database (type=COMPANY_DESC)")
        
        prompt = prompt_obj.description.strip()

        response = await self.client.chat.completions.create(
            model="gpt-4o-mini-search-preview-2025-03-11",
            messages=[
                {"role": "system", "content": prompt},
                {"role": "user", "content": f"Company Name: {company_name} Company URL: {company_url}"},
            ]
        )
        company_description = response.choices[0].message.content.strip()
        clean_company_description = self.remove_links(company_description)
        formated_respone = re.sub(
                r"\s{2,}", " ", re.sub(r"\n", "", re.sub(r"\\n", "", clean_company_description ))
            ).strip()

        return formated_respone
    
#     async def enhance_description(self, company_name: str, original_desc: str,company_url:str) -> str:
#         prompt = f"""
# You are an expert in employer branding and recruitment storytelling.

# Take the following rough company description and rewrite it to make it more compelling, professional, and attractive to top-tier tech candidates.

# Use the structure below and aim for clarity, energy, and employer branding excellence.

# - Company Name: {company_name}
# - Website: {company_url}
# - Original User Description: "{original_desc}"

# ### Output Style:
# - Start with a powerful opening sentence about the company's mission, product domain, and global reach
# - Describe the work culture, values, and what makes this an amazing place to grow
# - Add a bullet list of 4–5 points on why top candidates should join
# - End with one sentence that reinforces why this company is perfect for ambitious professionals
# - Do **NOT** use markdown, citations, or links
# """
#         response = await self.client.chat.completions.create(
#             model="gpt-4o-mini",
#             messages=[
#                 {"role": "system", "content": "You are a professional employer branding expert."},
#                 {"role": "user", "content": prompt}
#             ]
#         )
#         return response.choices[0].message.content.strip()
