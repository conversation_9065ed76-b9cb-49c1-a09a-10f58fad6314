from sqlalchemy import Column, Integer, String, Float, Text, DateTime,Boolean
from config.db import Base
from datetime import datetime
from sqlalchemy.orm import relationship





class Candidate(Base):
    __tablename__ = "candidates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    mobile_number = Column(String(15), nullable=False)
    resume_file_path = Column(Text, nullable=False)
    years_of_experience = Column(Float)
    current_location = Column(String(100))
    skills = Column(Text)
    parsed_resume_text = Column(Text, nullable=False)
    approved_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.now())

    employments = relationship("CandidateEmployment", back_populates="candidate", cascade="all, delete-orphan")


