import os
import fitz  # PyMuPDF
import docx

import os
import docx  # pip install python-docx
import fitz   # pip install pymupdf

def extract_resume_text(file_path: str) -> str:
    ext = os.path.splitext(file_path)[1].lower()
    text = []

    try:
        if ext == ".pdf":
            doc = fitz.open(file_path)
            text = [page.get_text().strip() for page in doc if page.get_text().strip()]
    
        elif ext == ".docx":
            doc = docx.Document(file_path)

            # Extract normal paragraphs
            for para in doc.paragraphs:
                para_text = para.text.strip()
                if para_text:
                    text.append(para_text)

            # Extract table content
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text and cell_text not in text:
                            text.append(cell_text)

        else:
            raise ValueError(f"Unsupported file format: {ext}")
    except Exception as e:
        print(f"[ERROR] Extracting {file_path}: {e}")

    return "\n".join(text)  # Return as a single clean string
