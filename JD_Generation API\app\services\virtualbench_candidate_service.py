from sqlalchemy.orm import Session
from models.virtualbench_candidates import VirtualBenchCandidate
from schemas.virtualbench_candidate import VirtualBenchCandidateCreate

def create_or_update_virtualbench_candidate(db: Session, data: VirtualBenchCandidateCreate):
    existing = db.query(VirtualBenchCandidate).filter_by(
        virtual_bench_id=data.virtual_bench_id,
        candidate_id=data.candidate_id
    ).first()

    if existing:
        existing.llm_score = data.llm_score
        existing.llm_analysis = data.llm_analysis
        existing.prompt_id = data.prompt_id
    else:
        existing = VirtualBenchCandidate(**data.dict())
        db.add(existing)

    db.commit()
    db.refresh(existing)
    return existing
