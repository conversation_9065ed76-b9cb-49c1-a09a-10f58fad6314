from pydantic import BaseModel
from uuid import UUID
from typing import Optional
from models.role import ExperienceLevel, DeveloperMode

class RoleBase(BaseModel):
    role_name: str
    experience_level: ExperienceLevel
    number_of_developers: int
    developer_mode: DeveloperMode
    duration: Optional[str]  # only for part-time or contract
    location: str

class RoleCreate(RoleBase):
    pass

class RoleResponse(RoleBase):
    role_id: int
    company_id: int

    class Config:
        orm_mode = True
