from fastapi import FastAPI
from routers import company, role, virtual_bench,candidates,resume_ranking,prompts
from config.db import Base,engine

app = FastAPI(title="AI JD Generator Platform")

Base.metadata.create_all(bind=engine)

# Register routes
app.include_router(company.router, prefix="/companies", tags=["Company"])
app.include_router(role.router, prefix="/roles", tags=["Role"])
app.include_router(virtual_bench.router, prefix="/virtual-bench", tags=["Virtual Bench"])
app.include_router(candidates.router,tags=["Candidates"])
app.include_router(resume_ranking.router,tags=["Resume Ranking"])
app.include_router(prompts.router,prefix="/prompts", tags=["Prompts"])