from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from config.db import get_db
from models.prompts import Prompt as PromptModel
from schemas.prompts import PromptCreate, PromptResponse

router = APIRouter()


@router.post("/", response_model=PromptResponse)
def create_prompt(prompt: PromptCreate, db: Session = Depends(get_db)):
    # Now checking uniqueness on (description, type)
    existing = db.query(PromptModel).filter(
        PromptModel.description == prompt.description,
        PromptModel.type == prompt.type
    ).first()
    if existing:
        raise HTTPException(
            status_code=409,
            detail="Prompt with the same description and type already exists."
        )

    new_prompt = PromptModel(**prompt.dict())
    db.add(new_prompt)
    db.commit()
    db.refresh(new_prompt)
    return new_prompt
