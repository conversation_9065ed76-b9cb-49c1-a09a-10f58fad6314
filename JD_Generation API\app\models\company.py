from sqlalchemy import Column, String, DateTime,Integer,Text
from sqlalchemy.dialects.mysql import CHAR
from datetime import datetime
import uuid

from config.db import Base

class Company(Base):
    __tablename__ = "companies"

    company_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    url = Column(String(255), nullable=False)
    description = Column(Text,nullable=True)
    headquarter = Column(String(255),nullable=False)
    created_at = Column(DateTime, default=datetime.now())
    updated_at = Column(DateTime, default=datetime.now())
