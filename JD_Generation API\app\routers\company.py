from fastapi import APIRouter, Depends, HTTPException,Body
from sqlalchemy.orm import Session
from typing import List
import os
from config.db import SessionLocal
from models.company import Company
from models.role import Role
from schemas.company import CompanyCreate, CompanyResponse,EnhancementInput
from services.company_desc_generator import AICompanyDescriber
from schemas.role import RoleResponse
from openai import AsyncOpenAI
from dotenv import load_dotenv

load_dotenv()


router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Creating New Company
@router.post("/", response_model=CompanyResponse)
def create_company(company: CompanyCreate, db: Session = Depends(get_db)):
    new_company = Company(**company.dict())
    db.add(new_company)
    db.commit()
    db.refresh(new_company)
    return new_company

# Retriving the All Companies
@router.get("/", response_model=List[CompanyResponse])
def get_all_companies(db: Session = Depends(get_db)):
    return db.query(Company).all()

# Getting Role for the Individual Companies
@router.get("/{company_id}/roles", response_model=List[RoleResponse])
def get_roles_for_company(company_id: int, db: Session = Depends(get_db)):
    company = db.query(Company).filter(Company.company_id == company_id).first()
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    roles = db.query(Role).filter(Role.company_id == company_id).all()
    return roles

# Generate the Company description
@router.post("/companies/{company_id}/generate-description")
async def generate_company_description(company_id: int, db: Session = Depends(get_db)):
    company = db.query(Company).filter(Company.company_id == company_id).first()
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    if not company.name or not company.url:
        raise HTTPException(status_code=400, detail="Company name and URL must be set")

    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise HTTPException(status_code=500, detail="Missing OPENAI_API_KEY")

    openai_client = AsyncOpenAI(api_key=api_key)
    describer = AICompanyDescriber(openai_client)

    try:
        description = await describer.generate_company_description(db,company.name, company.url)
        clean_description = describer.remove_links(description)
        company.description = clean_description
        db.commit()
        db.refresh(company)

        return {
            "company_id": company.company_id,
            "description": clean_description
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI generation failed: {str(e)}")

# Enhance the Previous Company Description
@router.post("/companies/{company_id}/enhance-description")
async def enhance_company_description(
    company_id: int,
    input: EnhancementInput = Body(...),
    db: Session = Depends(get_db)
):
    company = db.query(Company).filter(Company.company_id == company_id).first()
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Use description from frontend instead of DB
    original_desc = input.updated_description

    if not original_desc:
        raise HTTPException(status_code=400, detail="Description is required for enhancement")

    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise HTTPException(status_code=500, detail="Missing OPENAI_API_KEY")

    openai_client = AsyncOpenAI(api_key=api_key)
    describer = AICompanyDescriber(openai_client)

    try:
        enhanced = await describer.enhance_description(
            company_name=company.name,
            original_desc=original_desc,
            company_url=company.url
        )

        enhanced_description = describer.remove_links(enhanced)

        company.description = enhanced_description
        db.commit()
        db.refresh(company)

        return {
            "company_id": company.company_id,
            "enhanced_description": enhanced_description
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Enhancement failed: {str(e)}")