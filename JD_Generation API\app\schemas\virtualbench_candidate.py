from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class VirtualBenchCandidateCreate(BaseModel):
    virtual_bench_id: int
    candidate_id: int
    prompt_id: Optional[int] = None
    llm_score: Optional[str] = None
    llm_analysis: Optional[str] = None

class VirtualBenchCandidateResponse(BaseModel):
    id: int
    virtual_bench_id: int
    candidate_id: int
    llm_score: Optional[str] = None
    llm_analysis: Optional[str]
    created_at: datetime

    class Config:
        orm_mode = True
